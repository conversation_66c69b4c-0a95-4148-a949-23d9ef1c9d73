use crate::common::client::config::ShredstreamSubscriptionFilters;
use crate::config::endpoints::EndpointConfig;
use crate::config::endpoints::validate_unique_names;
use crate::config::logger::LoggerConfig;
use serde::Deserialize;
use serde::Serialize;
use validator::Validate;

#[derive(Debug, Deserialize, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
#[validate(schema(function = "validate_app_config"))]
pub struct AppConfig {
    #[validate(nested)]
    pub logger: LoggerConfig,

    #[validate(length(min = 1))]
    #[validate(nested)]
    pub endpoints: Vec<EndpointConfig>,

    pub filters: Option<ShredstreamSubscriptionFilters>,
}

fn validate_app_config(app_config: &AppConfig) -> Result<(), validator::ValidationError> {
    validate_unique_names(&app_config.endpoints)
}
